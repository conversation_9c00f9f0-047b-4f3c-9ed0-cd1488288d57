// Load recipes from backend
async function loadRecipes() {
  const response = await fetch('http://localhost:3000/recipes');
  const data = await response.json();
  recipes = data;
  showRecipes();
}

// Save recipe to backend
async function addRecipe(e) {
  e.preventDefault();

  const name = document.getElementById("recipeName").value;
  const ingredients = document.getElementById("ingredients").value;
  const steps = document.getElementById("steps").value;
  const time = document.getElementById("time").value;
  const image = document.getElementById("imageUrl").value;
  const type = document.querySelector('input[name="type"]:checked').value;

  const newRecipe = { name, ingredients, steps, time, image, type };

  // Send to backend
  await fetch('http://localhost:3000/recipes', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(newRecipe)
  });

  document.getElementById("recipeForm").reset();
  loadRecipes(); // reload updated recipes
}

// Search functionality
function searchRecipe() {
  const input = document.getElementById("searchInput").value.toLowerCase();
  const filtered = recipes.filter(recipe => recipe.name.toLowerCase().includes(input));
  showRecipes(filtered);
}

// Display recipes
function showRecipes(filteredRecipes = recipes) {
  const list = document.getElementById("recipeList");
  list.innerHTML = "";

  filteredRecipes.forEach(recipe => {
    const div = document.createElement("div");
    div.className = `recipe-card ${recipe.type === 'Non-Veg' ? 'non-veg' : ''}`;

    div.innerHTML = `
      <img src="${recipe.image}" alt="${recipe.name}">
      <div class="recipe-info">
        <h3>${recipe.name}</h3>
        <p><strong>Type:</strong> ${recipe.type}</p>
        <p><strong>Time:</strong> ${recipe.time}</p>
        <p><strong>Ingredients:</strong> ${recipe.ingredients}</p>
        <p><strong>Steps:</strong><br>${recipe.steps.replace(/\n/g, "<br>")}</p>
      </div>
    `;
    list.appendChild(div);
  });
}

// Global recipe list (fetched from backend)
let recipes = [];

loadRecipes(); // Initial call
