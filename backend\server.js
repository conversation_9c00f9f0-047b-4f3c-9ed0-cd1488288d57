const express = require('express');
const cors = require('cors');
const fs = require('fs');
const app = express();

app.use(cors());
app.use(express.json());

const filePath = './backend/recipes.json';

// GET all recipes
app.get('/recipes', (req, res) => {
  const data = JSON.parse(fs.readFileSync(filePath, 'utf8'));
  res.json(data);
});

// POST a new recipe
app.post('/recipes', (req, res) => {
  const recipes = JSON.parse(fs.readFileSync(filePath, 'utf8'));
  recipes.push(req.body);
  fs.writeFileSync(filePath, JSON.stringify(recipes, null, 2));
  res.json({ message: 'Recipe added successfully!' });
});

app.listen(3000, () => {
  console.log('✅ Backend running at http://localhost:3000');
});
