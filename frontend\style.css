body {
  font-family: Arial, sans-serif;
  background-color: #f2f2f2;
  padding: 20px;
}

.container {
  max-width: 800px;
  margin: auto;
  background: white;
  padding: 20px;
  border-radius: 10px;
}

input[type="text"], textarea {
  width: 100%;
  padding: 10px;
  margin-top: 10px;
  box-sizing: border-box;
}

button {
  margin-top: 10px;
  padding: 10px;
  background-color: #28a745;
  color: white;
  border: none;
  cursor: pointer;
}

button:hover {
  background-color: #218838;
}

.recipe-card {
  background-color: #e8f0fe;
  margin-top: 20px;
  padding: 15px;
  border-left: 5px solid green;
  display: flex;
  gap: 15px;
  border-radius: 8px;
}

.recipe-card.non-veg {
  border-left: 5px solid red;
}

.recipe-info {
  flex: 1;
}

.recipe-card img {
  width: 150px;
  height: 120px;
  object-fit: cover;
  border-radius: 8px;
}
